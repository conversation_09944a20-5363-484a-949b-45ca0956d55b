{"name": "axiia-mcp", "version": "1.0.0", "description": "Axiia MCP Server with OAuth authentication", "type": "module", "main": "dist/index.js", "scripts": {"build": "tsc && cp src/loginPage.html dist/", "start": "node dist/index.js", "start:prod-oauth": "node dist/index.js --oauth", "start:prod-oauth-strict": "node dist/index.js --oauth --oauth-strict", "dev": "tsx src/index.ts", "dev:oauth": "tsx src/index.ts --oauth", "dev:oauth-strict": "tsx src/index.ts --oauth --oauth-strict", "inspector": "npx @modelcontextprotocol/inspector"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.16.0", "express": "^5.0.1", "cors": "^2.8.5", "zod": "^3.23.8"}, "devDependencies": {"@types/express": "^5.0.0", "@types/cors": "^2.8.17", "@types/node": "^22.0.2", "tsx": "^4.16.5", "typescript": "^5.5.4"}, "engines": {"node": ">=18"}}