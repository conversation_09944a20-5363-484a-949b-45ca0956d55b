#!/usr/bin/env node

/**
 * Test script to verify login functionality
 */

const AUTH_SERVER_URL = 'http://localhost:3001';
const MCP_SERVER_URL = 'http://localhost:3000';

async function testLoginFlow() {
  console.log('Testing Login Flow...\n');

  try {
    // Step 1: Try to access authorization endpoint without login
    console.log('1. Testing authorization endpoint without login...');
    const authParams = new URLSearchParams({
      response_type: 'code',
      client_id: 'test-client-123',
      code_challenge: '8MGXJ8sPn_0OapW53h1CcjgF9TjCSa22zkSCdEhps1s',
      code_challenge_method: 'S256',
      redirect_uri: 'http://localhost:8080/callback',
      resource: `${MCP_SERVER_URL}/mcp`
    });

    const authUrl = `${AUTH_SERVER_URL}/authorize?${authParams.toString()}`;
    console.log(`   Accessing: ${authUrl}`);
    
    const authResponse = await fetch(authUrl, {
      method: 'GET',
      redirect: 'manual'
    });

    console.log(`   Response status: ${authResponse.status}`);
    
    if (authResponse.status === 200) {
      const responseText = await authResponse.text();
      if (responseText.includes('Axiia MCP')) {
        console.log('✅ Login page displayed correctly');
      } else {
        console.log('❌ Unexpected response content');
      }
    } else {
      console.log('❌ Expected login page, got different response');
    }

    console.log('');

    // Step 2: Test login with invalid credentials
    console.log('2. Testing login with invalid credentials...');
    const invalidLoginData = new URLSearchParams({
      username: 'invalid',
      password: 'wrong',
      client_id: 'test-client-123',
      redirect_uri: 'http://localhost:8080/callback',
      response_type: 'code'
    });

    const invalidLoginResponse = await fetch(`${AUTH_SERVER_URL}/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: invalidLoginData.toString(),
      redirect: 'manual'
    });

    console.log(`   Response status: ${invalidLoginResponse.status}`);
    
    if (invalidLoginResponse.status === 401) {
      const responseText = await invalidLoginResponse.text();
      if (responseText.includes('Invalid username or password')) {
        console.log('✅ Invalid login correctly rejected');
      } else {
        console.log('⚠️  Login rejected but error message not found');
      }
    } else {
      console.log('❌ Expected 401 status for invalid login');
    }

    console.log('');

    // Step 3: Test login with valid credentials
    console.log('3. Testing login with valid credentials...');
    const validLoginData = new URLSearchParams({
      username: 'admin',
      password: 'admin123',
      client_id: 'test-client-123',
      redirect_uri: 'http://localhost:8080/callback',
      response_type: 'code',
      code_challenge: '8MGXJ8sPn_0OapW53h1CcjgF9TjCSa22zkSCdEhps1s',
      code_challenge_method: 'S256',
      resource: `${MCP_SERVER_URL}/mcp`
    });

    const validLoginResponse = await fetch(`${AUTH_SERVER_URL}/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: validLoginData.toString(),
      redirect: 'manual'
    });

    console.log(`   Response status: ${validLoginResponse.status}`);
    
    if (validLoginResponse.status === 302) {
      const location = validLoginResponse.headers.get('location');
      const setCookie = validLoginResponse.headers.get('set-cookie');
      
      console.log('✅ Login successful! Redirected to:');
      console.log(`   ${location}`);
      
      if (setCookie && setCookie.includes('mcp-session')) {
        console.log('✅ Session cookie set correctly');
      } else {
        console.log('⚠️  Session cookie not found');
      }
    } else {
      console.log('❌ Expected redirect after successful login');
    }

    console.log('');
    console.log('🎉 Login flow test completed!');
    console.log('');
    console.log('📝 Summary:');
    console.log('   - Unauthorized access shows login page');
    console.log('   - Invalid credentials are rejected');
    console.log('   - Valid credentials create session and redirect');
    console.log('   - OAuth parameters are preserved through login flow');
    console.log('');
    console.log('🔑 Available test accounts:');
    console.log('   - admin / admin123');
    console.log('   - user / user123');
    console.log('   - demo / demo123');

  } catch (error) {
    console.error('❌ Login flow test failed:', error.message);
    process.exit(1);
  }
}

// Run the test
testLoginFlow();
