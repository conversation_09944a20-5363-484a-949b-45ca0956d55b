import express, { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import cors from 'cors';
import {
  OAuthServerProvider
} from '@modelcontextprotocol/sdk/server/auth/provider.js';
import {
  OAuthClientInformationFull
} from '@modelcontextprotocol/sdk/shared/auth.js';
import { AuthManager } from './authManager.js';

function allowedMethods(methods: string[]) {
  return (req: any, res: any, next: any) => {
    if (!methods.includes(req.method)) {
      return res.status(405).json({ error: 'Method not allowed' });
    }
    next();
  };
}

// Simple error classes
class OAuthError extends Error {
  constructor(public error: string, public error_description?: string) {
    super(error_description || error);
  }

  toResponseObject() {
    return {
      error: this.error,
      error_description: this.error_description
    };
  }
}

class InvalidRequestError extends OAuthError {
  constructor(description: string) {
    super('invalid_request', description);
  }
}

class InvalidClientError extends OAuthError {
  constructor(description: string) {
    super('invalid_client', description);
  }
}

class InvalidScopeError extends OAuthError {
  constructor(description: string) {
    super('invalid_scope', description);
  }
}

class ServerError extends OAuthError {
  constructor(description: string) {
    super('server_error', description);
  }
}

class TooManyRequestsError extends OAuthError {
  constructor(description: string) {
    super('too_many_requests', description);
  }
}

// Import the schemas we need
const ClientAuthorizationParamsSchema = {
  safeParse: (data: any) => {
    // Basic validation for client_id and redirect_uri
    if (!data.client_id) {
      return { success: false as const, error: { message: 'client_id is required' } };
    }
    return {
      success: true as const,
      data: {
        client_id: data.client_id,
        redirect_uri: data.redirect_uri
      }
    };
  }
};

const AuthorizationParamsSchema = {
  safeParse: (data: any) => {
    // Basic validation
    if (data.response_type && data.response_type !== 'code') {
      return { success: false as const, error: { message: 'Only response_type=code is supported' } };
    }
    return {
      success: true as const,
      data: {
        response_type: data.response_type,
        scope: data.scope,
        code_challenge: data.code_challenge,
        code_challenge_method: data.code_challenge_method,
        state: data.state,
        resource: data.resource
      }
    };
  }
};

function createErrorRedirect(redirectUri: string, error: OAuthError, state?: string): string {
  const url = new URL(redirectUri);
  url.searchParams.set('error', error.error);
  if (error.error_description) {
    url.searchParams.set('error_description', error.error_description);
  }
  if (state) {
    url.searchParams.set('state', state);
  }
  return url.toString();
}

export type AutoRegisterAuthorizationHandlerOptions = {
  provider: OAuthServerProvider;
  rateLimit?: any | false;
  autoRegisterClients?: boolean;
};

/**
 * Custom authorization handler that automatically registers clients if they don't exist
 */
export function autoRegisterAuthorizationHandler({
  provider,
  rateLimit: rateLimitConfig,
  autoRegisterClients = true,
}: AutoRegisterAuthorizationHandlerOptions): RequestHandler {
  const router = express.Router();

  // Configure CORS to allow any origin
  router.use(cors());

  router.use(allowedMethods(["GET", "POST"]));
  router.use(express.json());
  router.use(express.urlencoded({ extended: true }));

  // Apply simple rate limiting unless explicitly disabled
  if (rateLimitConfig !== false) {
    // Simple rate limiting implementation
    const requestCounts = new Map<string, { count: number, resetTime: number }>();

    router.use((req, res, next) => {
      const clientIp = req.ip || req.socket.remoteAddress || 'unknown';
      const now = Date.now();
      const windowMs = 15 * 60 * 1000; // 15 minutes
      const maxRequests = 100;

      const clientData = requestCounts.get(clientIp);
      if (!clientData || now > clientData.resetTime) {
        requestCounts.set(clientIp, { count: 1, resetTime: now + windowMs });
        next();
      } else if (clientData.count < maxRequests) {
        clientData.count++;
        next();
      } else {
        res.status(429).json(new TooManyRequestsError('You have exceeded the rate limit for authorization requests').toResponseObject());
      }
    });
  }

  router.all("/", async (req, res) => {
    res.setHeader('Cache-Control', 'no-store');

    // Phase 0: Check if user is logged in
    if (!AuthManager.isLoggedIn(req)) {
      // If this is a POST request to /login, handle the login
      if (req.method === 'POST' && req.path === '/login') {
        const loginSuccess = AuthManager.handleLogin(req, res);
        if (!loginSuccess) {
          return; // Response already sent by handleLogin
        }
        // Login successful, continue with authorization
      } else {
        // Show login page with OAuth parameters preserved
        const oauthParams = req.method === 'GET' ? req.query : req.body;
        const hiddenFields: Record<string, string> = {};

        // Preserve OAuth parameters
        const paramsToPreserve = [
          'client_id', 'redirect_uri', 'response_type', 'scope',
          'state', 'code_challenge', 'code_challenge_method', 'resource'
        ];

        for (const param of paramsToPreserve) {
          if (oauthParams[param]) {
            hiddenFields[param] = String(oauthParams[param]);
          }
        }

        const html = AuthManager.renderLoginPage(undefined, hiddenFields);
        res.send(html);
        return;
      }
    }

    // Phase 1: Validate client_id and redirect_uri
    let client_id: string, redirect_uri: string, client: OAuthClientInformationFull | undefined;
    let state: string | undefined;

    try {
      const result = ClientAuthorizationParamsSchema.safeParse(req.method === 'POST' ? req.body : req.query);
      if (!result.success) {
        throw new InvalidRequestError(result.error.message);
      }

      client_id = result.data.client_id;
      redirect_uri = result.data.redirect_uri;

      // Try to get existing client
      client = await provider.clientsStore.getClient(client_id);
      
      if (!client && autoRegisterClients) {
        console.log(`🔄 OAuth: Auto-registering client "${client_id}"`);
        
        // Auto-register the client with basic information
        const autoClientInfo: OAuthClientInformationFull = {
          client_id: client_id,
          redirect_uris: redirect_uri ? [redirect_uri] : ['http://localhost:8080/callback'],
          client_name: `Auto-registered client ${client_id}`,
          token_endpoint_auth_method: 'none', // Public client
          grant_types: ['authorization_code'],
          response_types: ['code'],
          scope: 'mcp:tools',
          client_id_issued_at: Math.floor(Date.now() / 1000),
        };

        // Register the client
        if (provider.clientsStore.registerClient) {
          client = await provider.clientsStore.registerClient(autoClientInfo);
          console.log(`✅ OAuth: Auto-registered client "${client_id}" with redirect_uri "${redirect_uri}"`);
        } else {
          throw new InvalidClientError("Client registration not supported");
        }
      }

      if (!client) {
        throw new InvalidClientError("Invalid client_id");
      }

      // Validate redirect_uri
      if (redirect_uri !== undefined) {
        if (!client.redirect_uris.includes(redirect_uri)) {
          // If auto-registration is enabled and this is a new redirect_uri, add it
          if (autoRegisterClients && provider.clientsStore.registerClient) {
            console.log(`🔄 OAuth: Adding new redirect_uri "${redirect_uri}" to client "${client_id}"`);
            client.redirect_uris.push(redirect_uri);
            client = await provider.clientsStore.registerClient(client);
          } else {
            throw new InvalidRequestError("Unregistered redirect_uri");
          }
        }
      } else if (client.redirect_uris.length === 1) {
        redirect_uri = client.redirect_uris[0];
      } else {
        throw new InvalidRequestError("redirect_uri must be specified when client has multiple registered URIs");
      }

    } catch (error) {
      // Pre-redirect errors - direct response with 400
      if (error instanceof OAuthError) {
        const status = error instanceof ServerError ? 500 : 400;
        res.status(status).json(error.toResponseObject());
      } else {
        const serverError = new ServerError("Internal Server Error");
        res.status(500).json(serverError.toResponseObject());
      }
      return;
    }

    // Phase 2: Validate authorization parameters
    try {
      const parseResult = AuthorizationParamsSchema.safeParse(req.method === 'POST' ? req.body : req.query);
      if (!parseResult.success) {
        throw new InvalidRequestError(parseResult.error.message);
      }

      const { scope, code_challenge, resource } = parseResult.data;
      state = parseResult.data.state;

      // Validate scopes
      let requestedScopes: string[] = [];
      if (scope !== undefined) {
        requestedScopes = scope.split(" ");
        const allowedScopes = new Set(client!.scope?.split(" "));
        
        // Check each requested scope against allowed scopes
        for (const scopeItem of requestedScopes) {
          if (!allowedScopes.has(scopeItem)) {
            console.log(`⚠️  OAuth: Client "${client_id}" requested unregistered scope "${scopeItem}", allowing it`);
            // In auto-register mode, we're more permissive with scopes
          }
        }
      }

      // All validation passed, proceed with authorization
      await provider.authorize(client!, {
        state,
        scopes: requestedScopes,
        redirectUri: redirect_uri,
        codeChallenge: code_challenge,
        resource: resource ? new URL(resource) : undefined,
      }, res);

    } catch (error) {
      // Post-redirect errors - redirect with error parameters
      if (error instanceof OAuthError) {
        res.redirect(302, createErrorRedirect(redirect_uri, error, state));
      } else {
        const serverError = new ServerError("Internal Server Error");
        res.redirect(302, createErrorRedirect(redirect_uri, serverError, state));
      }
    }
  });

  return router;
}
