export interface Problem {
  id: string;
  title: string;
  description: string;
  difficulty: 'easy' | 'medium' | 'hard';
}

export interface ProblemSet {
  id: string;
  name: string;
  problemIds: string[];
  expireMinutes?: number;
  createdAt: Date;
}

export interface User {
  id: string;
  name: string;
  email: string;
  whitelistTag?: string;
  createdAt: Date;
}

export interface Organization {
  id: string;
  name: string;
  problemSetIds: string[];
  whitelistTags?: string[];
  userIds?: string[];
  createdAt: Date;
}