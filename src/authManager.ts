import { randomUUID } from 'node:crypto';
import { Request, Response } from 'express';
import { readFileSync } from 'node:fs';
import { join, dirname } from 'node:path';
import { fileURLToPath } from 'node:url';

// Get __dirname equivalent in ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Fixed user accounts
const FIXED_USERS = {
  'admin': { password: 'admin123', role: 'admin', name: 'Administrator' },
};

// Session storage (should use Redis or database in production)
const sessions = new Map<string, {
  userId: string;
  username: string;
  role: string;
  name: string;
  createdAt: number;
  lastAccess: number;
}>();

// Session timeout (30 minutes)
const SESSION_TIMEOUT = 30 * 60 * 1000;

export class AuthManager {
  /**
   * Validate user credentials
   */
  static validateUser(username: string, password: string): boolean {
    const user = FIXED_USERS[username as keyof typeof FIXED_USERS];
    return user && user.password === password;
  }

  /**
   * Create user session
   */
  static createSession(username: string): string {
    const user = FIXED_USERS[username as keyof typeof FIXED_USERS];
    if (!user) {
      throw new Error('Invalid user');
    }

    const sessionId = randomUUID();
    const now = Date.now();

    sessions.set(sessionId, {
      userId: randomUUID(),
      username,
      role: user.role,
      name: user.name,
      createdAt: now,
      lastAccess: now,
    });

    console.log(`✅ Created session for user: ${username} (${user.name})`);
    return sessionId;
  }

  /**
   * Validate session
   */
  static validateSession(sessionId: string): boolean {
    const session = sessions.get(sessionId);
    if (!session) {
      return false;
    }

    const now = Date.now();
    if (now - session.lastAccess > SESSION_TIMEOUT) {
      sessions.delete(sessionId);
      console.log(`⏰ Session expired for user: ${session.username}`);
      return false;
    }

    // Update last access time
    session.lastAccess = now;
    return true;
  }

  /**
   * Get session information
   */
  static getSession(sessionId: string) {
    const session = sessions.get(sessionId);
    if (!session || !this.validateSession(sessionId)) {
      return null;
    }
    return session;
  }

  /**
   * Destroy session
   */
  static destroySession(sessionId: string): void {
    const session = sessions.get(sessionId);
    if (session) {
      console.log(`🗑️  Destroyed session for user: ${session.username}`);
      sessions.delete(sessionId);
    }
  }

  /**
   * Get session ID from request
   */
  static getSessionIdFromRequest(req: Request): string | null {
    // Get session ID from cookie
    const sessionId = req.cookies?.['mcp-session'];
    return sessionId || null;
  }

  /**
   * Check if user is logged in
   */
  static isLoggedIn(req: Request): boolean {
    const sessionId = this.getSessionIdFromRequest(req);
    return sessionId ? this.validateSession(sessionId) : false;
  }

  /**
   * Render login page
   */
  static renderLoginPage(errorMessage?: string, hiddenFields?: Record<string, string>): string {
    const loginPagePath = join(__dirname, 'loginPage.html');
    let html = readFileSync(loginPagePath, 'utf-8');

    // Replace error message
    const errorHtml = errorMessage
      ? `<div class="error-message">${errorMessage}</div>`
      : '';
    html = html.replace('{{ERROR_MESSAGE}}', errorHtml);

    // Replace hidden fields
    let hiddenFieldsHtml = '';
    if (hiddenFields) {
      for (const [key, value] of Object.entries(hiddenFields)) {
        hiddenFieldsHtml += `<input type="hidden" name="${key}" value="${value}">`;
      }
    }
    html = html.replace('{{HIDDEN_FIELDS}}', hiddenFieldsHtml);

    return html;
  }

  /**
   * Render success page after login
   */
  static renderSuccessPage(username: string, redirectUrl: string, delay: number = 3): string {
    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login Successful - Axiia MCP</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .success-container {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 500px;
            text-align: center;
        }

        .success-icon {
            font-size: 4rem;
            color: #28a745;
            margin-bottom: 1rem;
        }

        .success-title {
            color: #333;
            font-size: 1.8rem;
            margin-bottom: 1rem;
        }

        .success-message {
            color: #666;
            font-size: 1rem;
            margin-bottom: 2rem;
            line-height: 1.5;
        }

        .user-info {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 5px;
            margin-bottom: 2rem;
            border-left: 4px solid #28a745;
        }

        .user-info h3 {
            color: #333;
            font-size: 1rem;
            margin-bottom: 0.5rem;
        }

        .user-info p {
            color: #666;
            font-size: 0.9rem;
        }

        .redirect-info {
            background: #e3f2fd;
            padding: 1rem;
            border-radius: 5px;
            border-left: 4px solid #2196f3;
        }

        .redirect-info p {
            color: #666;
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
        }

        .countdown {
            font-weight: bold;
            color: #2196f3;
            font-size: 1.1rem;
        }

        .manual-link {
            margin-top: 1rem;
        }

        .manual-link a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
        }

        .manual-link a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="success-container">
        <div class="success-icon">✅</div>
        <h1 class="success-title">Authentication Successful!</h1>
        <p class="success-message">
            You have been successfully authenticated and logged into the Axiia MCP system.
        </p>

        <div class="user-info">
            <h3>Welcome, ${username}!</h3>
            <p>Your session has been created and you now have access to MCP services.</p>
        </div>

        <div class="redirect-info">
            <p>You will be automatically redirected to continue the authorization process in <span class="countdown" id="countdown">${delay}</span> seconds.</p>
            <div class="manual-link">
                <a href="${redirectUrl}" id="continueLink">Click here to continue immediately</a>
            </div>
        </div>
    </div>

    <script>
        let timeLeft = ${delay};
        const countdownElement = document.getElementById('countdown');
        const continueLink = document.getElementById('continueLink');

        const timer = setInterval(() => {
            timeLeft--;
            countdownElement.textContent = timeLeft;

            if (timeLeft <= 0) {
                clearInterval(timer);
                window.location.href = '${redirectUrl}';
            }
        }, 1000);

        // Allow immediate redirect on click
        continueLink.addEventListener('click', (e) => {
            e.preventDefault();
            clearInterval(timer);
            window.location.href = '${redirectUrl}';
        });
    </script>
</body>
</html>`;
  }

  /**
   * Handle login request
   */
  static handleLogin(req: Request, res: Response): boolean {
    const { username, password } = req.body;

    if (!username || !password) {
      const hiddenFields = this.extractHiddenFields(req.body);
      const html = this.renderLoginPage('Please enter username and password', hiddenFields);
      res.status(400).send(html);
      return false;
    }

    if (!this.validateUser(username, password)) {
      const hiddenFields = this.extractHiddenFields(req.body);
      const html = this.renderLoginPage('Invalid username or password', hiddenFields);
      res.status(401).send(html);
      return false;
    }

    // Create session
    const sessionId = this.createSession(username);

    // Set session cookie
    res.cookie('mcp-session', sessionId, {
      httpOnly: true,
      secure: false, // Should be true in production (HTTPS)
      maxAge: SESSION_TIMEOUT,
      sameSite: 'lax'
    });

    return true;
  }

  /**
   * Handle login request with success page
   */
  static handleLoginWithSuccessPage(req: Request, res: Response): void {
    const { username, password } = req.body;

    if (!username || !password) {
      const hiddenFields = this.extractHiddenFields(req.body);
      const html = this.renderLoginPage('Please enter username and password', hiddenFields);
      res.status(400).send(html);
      return;
    }

    if (!this.validateUser(username, password)) {
      const hiddenFields = this.extractHiddenFields(req.body);
      const html = this.renderLoginPage('Invalid username or password', hiddenFields);
      res.status(401).send(html);
      return;
    }

    // Create session
    const sessionId = this.createSession(username);

    // Set session cookie
    res.cookie('mcp-session', sessionId, {
      httpOnly: true,
      secure: false, // Should be true in production (HTTPS)
      maxAge: SESSION_TIMEOUT,
      sameSite: 'lax'
    });

    // Build redirect URL with OAuth parameters
    const oauthParams = new URLSearchParams();
    const paramsToPreserve = [
      'client_id', 'redirect_uri', 'response_type', 'scope',
      'state', 'code_challenge', 'code_challenge_method', 'resource'
    ];

    for (const param of paramsToPreserve) {
      if (req.body[param]) {
        oauthParams.set(param, req.body[param]);
      }
    }

    const redirectUrl = `/authorize?${oauthParams.toString()}`;

    // Show success page with auto-redirect
    const html = this.renderSuccessPage(username, redirectUrl, 3);
    res.send(html);
  }

  /**
   * Extract hidden fields (for preserving OAuth parameters)
   */
  private static extractHiddenFields(body: any): Record<string, string> {
    const hiddenFields: Record<string, string> = {};
    const oauthParams = [
      'client_id', 'redirect_uri', 'response_type', 'scope', 
      'state', 'code_challenge', 'code_challenge_method', 'resource'
    ];

    for (const param of oauthParams) {
      if (body[param]) {
        hiddenFields[param] = body[param];
      }
    }

    return hiddenFields;
  }

  /**
   * Clean up expired sessions (called periodically)
   */
  static cleanupExpiredSessions(): void {
    const now = Date.now();
    let cleanedCount = 0;

    for (const [sessionId, session] of sessions.entries()) {
      if (now - session.lastAccess > SESSION_TIMEOUT) {
        sessions.delete(sessionId);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      console.log(`🧹 Cleaned up ${cleanedCount} expired sessions`);
    }
  }
}

// Clean up expired sessions every 5 minutes
setInterval(() => {
  AuthManager.cleanupExpiredSessions();
}, 5 * 60 * 1000);
