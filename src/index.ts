import express from 'express';
import { z } from 'zod';
import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import { 
  getOAuthProtectedResourceMetadataUrl, 
  mcpAuthMetadataRouter 
} from '@modelcontextprotocol/sdk/server/auth/router.js';
import { requireBearerAuth } from '@modelcontextprotocol/sdk/server/auth/middleware/bearerAuth.js';
import { 
  CallToolResult, 
  GetPromptResult, 
  ReadResourceResult, 
} from '@modelcontextprotocol/sdk/types.js';
import { setupAuthServer } from './demoInMemoryOAuthProvider.js';
import { OAuthMetadata } from '@modelcontextprotocol/sdk/shared/auth.js';
import { checkResourceAllowed } from '@modelcontextprotocol/sdk/shared/auth-utils.js';
import { db } from './database';


import cors from 'cors';

// Check for OAuth flag
const useOAuth = process.argv.includes('--oauth');
const strictOAuth = process.argv.includes('--oauth-strict');

// Create an MCP server with implementation details
const getServer = () => {
  const server = new McpServer({
    name: 'axiia-mcp-demo',
    version: '1.0.0'
  }, { capabilities: { logging: {} } });

  // Register a simple tool that returns a greeting
  server.registerTool(
    'greet',
    {
      title: 'Greeting Tool',  // Display name for UI
      description: 'A simple greeting tool',
      inputSchema: {
        name: z.string().describe('Name to greet'),
      },
    },
    async ({ name }): Promise<CallToolResult> => {
      return {
        content: [
          {
            type: 'text',
            text: `Hello, ${name}!`,
          },
        ],
      };
    }
  );

server.registerTool('get_all_problem_ids', {
  description: 'Get all problem IDs from the database',
  inputSchema: {
    type: 'object',
    properties: {},
    required: [],
  },
}, async () => {
  const problemIds = db.getAllProblemIds();
  return {
    content: [
      {
        type: 'text',
        text: JSON.stringify({ problemIds }, null, 2),
      },
    ],
  };
});

server.registerTool('get_all_problem_sets', {
  description: 'Get all problem sets from the database',
  inputSchema: {
    type: 'object',
    properties: {},
    required: [],
  },
}, async () => {
  const problemSets = db.getAllProblemSets();
  return {
    content: [
      {
        type: 'text',
        text: JSON.stringify({ problemSets }, null, 2),
      },
    ],
  };
});

server.registerTool('create_problem_set', {
  description: 'Create a new problem set with given problem IDs',
  inputSchema: {
    type: 'object',
    properties: {
      name: {
        type: 'string',
        description: 'Name of the problem set',
      },
      problemIds: {
        type: 'array',
        items: { type: 'string' },
        description: 'Array of problem IDs to include in the set',
      },
      expireMinutes: {
        type: 'number',
        description: 'Optional expiration time in minutes',
      },
    },
    required: ['name', 'problemIds'],
  },
}, async (args) => {
  const { name, problemIds, expireMinutes } = args as {
    name: string;
    problemIds: string[];
    expireMinutes?: number;
  };

  const problemSet = db.createProblemSet(name, problemIds, expireMinutes);
  return {
    content: [
      {
        type: 'text',
        text: JSON.stringify({ success: true, problemSet }, null, 2),
      },
    ],
  };
});

server.registerTool('create_user', {
  description: 'Create a user or multiple users',
  inputSchema: {
    type: 'object',
    properties: {
      users: {
        type: 'array',
        items: {
          type: 'object',
          properties: {
            name: { type: 'string' },
            email: { type: 'string' },
            whitelistTag: { type: 'string' },
          },
          required: ['name', 'email'],
        },
        description: 'Array of user data for batch creation',
      },
    },
    required: ['users'],
  },
}, async (args) => {
  const { users } = args as {
    users: Array<{ name: string; email: string; whitelistTag?: string }>;
  };

  const createdUsers = db.createUsers(users);
  return {
    content: [
      {
        type: 'text',
        text: JSON.stringify({ success: true, users: createdUsers }, null, 2),
      },
    ],
  };
});

server.registerTool('create_organization', {
  description: 'Create an organization with associated problem sets and users/whitelist tags',
  inputSchema: {
    type: 'object',
    properties: {
      name: {
        type: 'string',
        description: 'Name of the organization',
      },
      problemSetIds: {
        type: 'array',
        items: { type: 'string' },
        description: 'Array of problem set IDs to associate with the organization',
      },
      whitelistTags: {
        type: 'array',
        items: { type: 'string' },
        description: 'Optional array of whitelist tags',
      },
      userIds: {
        type: 'array',
        items: { type: 'string' },
        description: 'Optional array of user IDs',
      },
    },
    required: ['name', 'problemSetIds'],
  },
}, async (args) => {
  const { name, problemSetIds, whitelistTags, userIds } = args as {
    name: string;
    problemSetIds: string[];
    whitelistTags?: string[];
    userIds?: string[];
  };

  const organization = db.createOrganization(name, problemSetIds, whitelistTags, userIds);
  return {
    content: [
      {
        type: 'text',
        text: JSON.stringify({ success: true, organization }, null, 2),
      },
    ],
  };
});

server.registerResource('problems://all', {
  name: 'All Problems',
  description: 'List of all available problems',
  mimeType: 'application/json',
}, async () => {
  const problemIds = db.getAllProblemIds();
  return {
    contents: [
      {
        uri: 'problems://all',
        mimeType: 'application/json',
        text: JSON.stringify({ problemIds }, null, 2),
      },
    ],
  };
});

server.registerResource('problem-sets://all', {
  name: 'All Problem Sets',
  description: 'List of all problem sets',
  mimeType: 'application/json',
}, async () => {
  const problemSets = db.getAllProblemSets();
  return {
    contents: [
      {
        uri: 'problem-sets://all',
        mimeType: 'application/json',
        text: JSON.stringify({ problemSets }, null, 2),
      },
    ],
  };
});


  return server;
};

const MCP_PORT = process.env.MCP_PORT ? parseInt(process.env.MCP_PORT, 10) : 3000;
const AUTH_PORT = process.env.MCP_AUTH_PORT ? parseInt(process.env.MCP_AUTH_PORT, 10) : 3001;

const app = express();
app.use(express.json());

// Allow CORS all domains, expose the Mcp-Session-Id header
app.use(cors({
  origin: '*', // Allow all origins
  exposedHeaders: ["Mcp-Session-Id"]
}));

// Set up OAuth if enabled
let authMiddleware = null;
if (useOAuth) {
  // Create auth middleware for MCP endpoints
  const mcpServerUrl = new URL(`http://localhost:${MCP_PORT}/mcp`);
  const authServerUrl = new URL(`http://localhost:${AUTH_PORT}`);

  const oauthMetadata: OAuthMetadata = setupAuthServer({ authServerUrl, mcpServerUrl, strictResource: strictOAuth });

  const tokenVerifier = {
    verifyAccessToken: async (token: string) => {
      const endpoint = oauthMetadata.introspection_endpoint;

      if (!endpoint) {
        throw new Error('No token verification endpoint available in metadata');
      }

      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          token: token
        }).toString()
      });


      if (!response.ok) {
        throw new Error(`Invalid or expired token: ${await response.text()}`);
      }

      const data = await response.json();

      if (strictOAuth) {
        if (!data.aud) {
          throw new Error(`Resource Indicator (RFC8707) missing`);
        }
        if (!checkResourceAllowed({ requestedResource: data.aud, configuredResource: mcpServerUrl })) {
          throw new Error(`Expected resource indicator ${mcpServerUrl}, got: ${data.aud}`);
        }
      }

      // Convert the response to AuthInfo format
      return {
        token,
        clientId: data.client_id,
        scopes: data.scope ? data.scope.split(' ') : [],
        expiresAt: data.exp,
      };
    }
  }
  // Add metadata routes to the main MCP server
  app.use(mcpAuthMetadataRouter({
    oauthMetadata,
    resourceServerUrl: mcpServerUrl,
    scopesSupported: ['mcp:tools'],
    resourceName: 'MCP Demo Server',
  }));

  authMiddleware = requireBearerAuth({
    verifier: tokenVerifier,
    requiredScopes: [],
    resourceMetadataUrl: getOAuthProtectedResourceMetadataUrl(mcpServerUrl),
  });
}

// Import server handlers
import {
  createMcpPostHandler,
  createMcpGetHandler,
  createMcpDeleteHandler,
  setupShutdownHandler
} from './server-handlers.js';

// Create handlers
const mcpPostHandler = createMcpPostHandler(getServer, useOAuth);
const mcpGetHandler = createMcpGetHandler(useOAuth);
const mcpDeleteHandler = createMcpDeleteHandler();

// Set up routes with conditional auth middleware
if (useOAuth && authMiddleware) {
  app.post('/mcp', authMiddleware, mcpPostHandler);
  app.get('/mcp', authMiddleware, mcpGetHandler);
  app.delete('/mcp', authMiddleware, mcpDeleteHandler);
} else {
  app.post('/mcp', mcpPostHandler);
  app.get('/mcp', mcpGetHandler);
  app.delete('/mcp', mcpDeleteHandler);
}

app.listen(MCP_PORT, (error) => {
  if (error) {
    console.error('Failed to start server:', error);
    process.exit(1);
  }
  console.log(`MCP Streamable HTTP Server listening on port ${MCP_PORT}`);
});

// Set up shutdown handler
setupShutdownHandler();
