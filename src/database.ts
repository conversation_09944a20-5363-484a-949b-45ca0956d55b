import { Problem, ProblemSet, User, Organization } from './types';

export class Database {
  private problems: Map<string, Problem> = new Map();
  private problemSets: Map<string, ProblemSet> = new Map();
  private users: Map<string, User> = new Map();
  private organizations: Map<string, Organization> = new Map();

  constructor() {
    this.initializeMockData();
  }

  private initializeMockData() {
    const mockProblems: Problem[] = [
      { id: 'p1', title: 'Two Sum', description: 'Find two numbers that add up to target', difficulty: 'easy' },
      { id: 'p2', title: 'Reverse Linked List', description: 'Reverse a singly linked list', difficulty: 'easy' },
      { id: 'p3', title: 'Binary Tree Inorder', description: 'Inorder traversal of binary tree', difficulty: 'medium' },
      { id: 'p4', title: 'Merge Sort', description: 'Implement merge sort algorithm', difficulty: 'medium' },
      { id: 'p5', title: 'Graph Algorithms', description: 'Advanced graph traversal', difficulty: 'hard' }
    ];

    mockProblems.forEach(problem => this.problems.set(problem.id, problem));
  }

  getAllProblemIds(): string[] {
    return Array.from(this.problems.keys());
  }

  getAllProblemSets(): ProblemSet[] {
    return Array.from(this.problemSets.values());
  }

  createProblemSet(name: string, problemIds: string[], expireMinutes?: number): ProblemSet {
    const id = `ps_${Date.now()}`;
    const problemSet: ProblemSet = {
      id,
      name,
      problemIds,
      expireMinutes,
      createdAt: new Date()
    };
    this.problemSets.set(id, problemSet);
    return problemSet;
  }

  createUser(name: string, email: string, whitelistTag?: string): User {
    const id = `u_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const user: User = {
      id,
      name,
      email,
      whitelistTag,
      createdAt: new Date()
    };
    this.users.set(id, user);
    return user;
  }

  createUsers(userData: Array<{ name: string; email: string; whitelistTag?: string }>): User[] {
    return userData.map(data => this.createUser(data.name, data.email, data.whitelistTag));
  }

  createOrganization(name: string, problemSetIds: string[], whitelistTags?: string[], userIds?: string[]): Organization {
    const id = `org_${Date.now()}`;
    const organization: Organization = {
      id,
      name,
      problemSetIds,
      whitelistTags,
      userIds,
      createdAt: new Date()
    };
    this.organizations.set(id, organization);
    return organization;
  }
}

export const db = new Database();