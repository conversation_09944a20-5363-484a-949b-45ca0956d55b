# Simple Streamable HTTP Server

This is a standalone implementation of the Simple Streamable HTTP Server example from the MCP TypeScript SDK. It demonstrates how to create an MCP (Model Context Protocol) server using the Streamable HTTP transport.

## Features

- **Tools**: Multiple example tools including greeting, multi-greeting with notifications, user info collection, and notification streaming
- **Resources**: Example resources with ResourceLink support
- **Prompts**: Template prompts for greeting
- **OAuth Support**: Optional OAuth authentication (demo implementation)
- **User Authentication**: Simple login system with fixed accounts
- **Session Management**: Cookie-based session management
- **Resumability**: Session resumability with event store
- **CORS Support**: Cross-origin resource sharing enabled

## Prerequisites

This project requires Node.js 18 or higher. If you don't have Node.js installed:

1. Install Node.js from [nodejs.org](https://nodejs.org/) or use a package manager like Homebrew:

   ```bash
   brew install node
   ```

2. Or use nvm (Node Version Manager):
   ```bash
   curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
   nvm install 18
   nvm use 18
   ```

## Installation

1. Install dependencies:

```bash
npm install
```

2. Build the project:

```bash
npm run build
```

## Alternative: Run using typescript-sdk dependencies

If you have the original typescript-sdk repository available, you can run this standalone version using the SDK's dependencies:

```bash
# Run the standalone server using SDK dependencies
./run-with-sdk.sh

# Run with OAuth enabled
./run-with-sdk.sh --oauth

# Run with strict OAuth
./run-with-sdk.sh --oauth --oauth-strict
```

Or run the original example from typescript-sdk:

```bash
cd ../typescript-sdk
npm run examples:simple-server:w
```

## Quick Start with Enterprise Login

For a complete test including OAuth and enterprise login:

```bash
npm run start:login
```

This will:

1. Start the server with OAuth and login enabled
2. Test the login functionality
3. Show you how to access the login-protected authorization
4. Keep the server running for testing

## Quick Start with OAuth Only

For OAuth testing without login requirements:

```bash
npm run start:oauth
```

This will:

1. Start the server with OAuth enabled (no login required)
2. Automatically test dynamic client registration
3. Show you the registered client_id and authorization URL
4. Keep the server running for testing

## Usage

### Development Mode

Run in development mode with hot reload:

```bash
npm run dev
```

Run with OAuth enabled:

```bash
npm run dev:oauth
```

Run with strict OAuth (Resource Indicator RFC8707):

```bash
npm run dev:oauth-strict
```

### Production Mode

Build and run:

```bash
npm run build
npm start
```

## Environment Variables

- `MCP_PORT`: Port for the MCP server (default: 3000)
- `MCP_AUTH_PORT`: Port for the OAuth authorization server (default: 3001)

## API Endpoints

- `POST /mcp` - Main MCP endpoint for JSON-RPC requests
- `GET /mcp` - Server-Sent Events (SSE) endpoint for streaming
- `DELETE /mcp` - Session termination endpoint

## Available Tools

1. **greet** - Simple greeting tool
2. **multi-greet** - Multiple greetings with notifications and delays
3. **collect-user-info** - Demonstrates user input collection (elicitation)
4. **start-notification-stream** - Sends periodic notifications for testing resumability
5. **list-files** - Returns ResourceLinks to example files

## Available Resources

1. **greeting-resource** - Simple text greeting
2. **example-file-1** - Example file content
3. **example-file-2** - Another example file

## Available Prompts

1. **greeting-template** - Template for generating greetings

## OAuth Authentication

When OAuth is enabled (`--oauth` flag), the server sets up a demo OAuth provider on a separate port. This implementation supports **Dynamic Client Registration** according to RFC 7591.

### OAuth Flow

1. **Client Discovery**: Clients discover OAuth metadata at `http://localhost:3001/.well-known/oauth-authorization-server`
2. **Client Registration**: Clients register themselves at `http://localhost:3001/register`
3. **Authorization**: Clients use the authorization endpoint with their registered client_id
4. **Token Exchange**: Clients exchange authorization codes for access tokens

### Testing OAuth Flow

Run the test script to verify OAuth functionality:

```bash
# Start the server with OAuth enabled
npm run dev:oauth

# In another terminal, test the OAuth flow
node test-oauth.js
```

### Manual Client Registration

To register a client manually:

```bash
curl -X POST http://localhost:3001/register \
  -H "Content-Type: application/json" \
  -d '{
    "redirect_uris": ["your://app/callback"],
    "client_name": "Your MCP Client",
    "token_endpoint_auth_method": "none",
    "grant_types": ["authorization_code"],
    "response_types": ["code"],
    "scope": "mcp:tools"
  }'
```

The response will include your `client_id` which you can then use in authorization requests.

### Auto-Registration Feature

🎉 **New**: This server now supports **automatic client registration**!

When a client tries to authorize with an unregistered `client_id`, the server will automatically register it with sensible defaults. This means:

- **Cursor, Claude Desktop, and other MCP clients work out of the box**
- No need to manually register clients first
- The server automatically creates client entries with appropriate redirect URIs

### Error: "invalid_client" (Legacy)

If you see `{"error":"invalid_client","error_description":"Invalid client_id"}`, it means:

1. Auto-registration is disabled, or
2. There was an error during auto-registration

With auto-registration enabled (default), this error should be rare.

## User Authentication

🔐 **Enterprise Login System**: This server now includes a simple user authentication system for enterprise use.

### Login Flow

1. **Access Control**: All OAuth authorization requests require user login
2. **Login Page**: Users see a branded login page when accessing the authorization endpoint
3. **Session Management**: Cookie-based sessions with 30-minute timeout
4. **Parameter Preservation**: OAuth parameters are preserved through the login flow

### Fixed User Accounts

The server includes these pre-configured accounts for testing:

| Username | Password   | Role  | Description   |
| -------- | ---------- | ----- | ------------- |
| `admin`  | `admin123` | admin | Administrator |

### Testing Login

```bash
# Test the login functionality
npm run dev:oauth
# In another terminal:
npm run test:login

# Test session lifecycle (login → logout → re-authentication)
npm run test:session
```

### Login Endpoints

- `GET /authorize` - Shows login page if not authenticated, then proceeds with OAuth
- `POST /login` - Handles login form submission
- `POST /logout` - Destroys user session

### Security Features

- **Session Timeout**: 30-minute automatic logout
- **Session Cleanup**: Automatic cleanup of expired sessions
- **Parameter Preservation**: OAuth state maintained through login
- **Cookie Security**: HttpOnly cookies (secure in production)

### Session Lifecycle

**Important**: After logout, users need to re-authenticate when connecting to MCP again.

#### Session State Management:

1. **Login Success** → Creates 30-minute valid session
2. **During Valid Session** → Direct access to MCP service, no re-authentication needed
3. **Session Invalidation Cases**:

   - 🚪 User actively logs out (`POST /logout`)
   - ⏰ 30-minute inactivity timeout
   - 🔄 Server restart (in-memory sessions lost)
   - 🗑️ User clears browser cookies

4. **After Session Invalidation** → Re-authentication required for access

#### Testing Session Lifecycle:

```bash
npm run test:session
```

This test demonstrates:

- Login → Authorization success
- Logout → Session destroyed
- Re-attempt authorization → Re-authentication required

**⚠️ Warning**: The OAuth implementation is for demo purposes only and lacks production features like persistent token storage and rate limiting.

## Architecture

The server uses:

- **Express.js** for HTTP handling
- **MCP TypeScript SDK** for protocol implementation
- **StreamableHTTPServerTransport** for bidirectional communication
- **InMemoryEventStore** for session resumability
- **Zod** for schema validation

## Session Management

The server maintains session state using session IDs passed in the `Mcp-Session-Id` header. Sessions support:

- Resumability after disconnection
- Event replay using Last-Event-ID
- Automatic cleanup on session termination

## Development

The project structure:

```
src/
├── index.ts                    # Main server file
├── server-handlers.ts          # HTTP request handlers
├── inMemoryEventStore.ts       # Event store for resumability
└── demoInMemoryOAuthProvider.ts # OAuth demo implementation
```

## License

This project is based on the MCP TypeScript SDK examples and follows the same MIT license.
